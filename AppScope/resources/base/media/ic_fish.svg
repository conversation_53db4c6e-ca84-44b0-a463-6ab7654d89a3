<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>ic_master_ai_fish</title>
    <defs>
        <path d="M5.52125245,14.8734595 C6.3247266,14.0744855 5.59657815,11.5277556 6.42516087,10.6788456 C6.92733221,10.1794868 8.15765201,10.0546471 8.71004049,9.97974332 L8.71004049,9.97974332 C9.51351464,9.87987156 10.4174231,9.60522423 11.2962229,9.13083337 C11.8988285,9.92980744 12.4763256,10.5540059 12.8027369,10.4791021 C13.4304511,10.3542624 13.4806683,8.65644251 13.4806683,7.65772492 C13.380234,7.63275698 13.2546912,7.60778904 13.1542569,7.5828211 C13.0789312,7.55785316 13.0287141,7.55785316 12.9533884,7.53288522 C12.8780626,7.50791728 12.8278455,7.50791728 12.7525198,7.48294934 C12.7023027,7.4579814 12.6771941,7.4579814 12.626977,7.43301346 C12.4763256,7.38307758 12.3256742,7.3331417 12.1750228,7.25823788 C12.1248056,7.23326994 12.0745885,7.208302 12.0243714,7.208302 C11.9490457,7.18333406 11.87372,7.13339818 11.7983943,7.10843024 C11.7481771,7.0834623 11.7230686,7.05849436 11.6728514,7.03352642 C11.5975257,6.98359054 11.5222,6.9586226 11.4468743,6.90868672 C11.3966572,6.88371878 11.3715486,6.85875084 11.3213315,6.8337829 C11.2460058,6.78384702 11.1706801,6.73391114 11.0953544,6.68397526 C11.0702458,6.65900732 11.0451372,6.63403938 10.9949201,6.60907144 C10.9195944,6.55913556 10.8442687,6.48423174 10.7438344,6.43429586 L10.7438344,6.43429586 C10.5178573,6.25952028 10.3169888,6.0847447 10.0910117,5.88500119 C9.68927461,5.48551415 9.33775467,5.03609123 9.06156043,4.53673243 C8.98623473,4.41189273 8.91090902,4.26208509 8.83558332,4.11227745 C8.71004049,3.86259806 8.81047476,3.53801484 9.08666899,3.41317514 C9.33775467,3.28833544 9.66416604,3.3882072 9.78970888,3.66285454 C9.83992601,3.78769424 9.91525171,3.887566 9.96546885,4.01240569 C10.2165545,4.41189273 10.4927488,4.81137977 10.8442687,5.13596299 C11.69796,5.98487294 12.7776284,6.48423174 13.8824053,6.65900732 C15.238268,4.51176449 15.2131594,2.03993845 14.5854452,1.41573995 C13.8321882,0.666701752 10.4174231,0.766573512 8.05721774,3.08859192 C8.05721774,3.08859192 8.05721774,3.08859192 8.05721774,3.08859192 C7.95678347,3.18846368 7.83124063,3.31330338 7.73080636,3.43814308 L7.73080636,3.43814308 C7.73080636,3.43814308 5.06929824,3.58795072 4.89353826,4.38692479 C4.81821256,4.71150801 5.49614388,5.31073857 6.29961803,5.88500119 C6.12385806,6.38435998 6.02342379,6.85875084 5.94809809,7.30817376 L5.94809809,7.30817376 C5.87277239,7.85746843 5.74722955,9.10586543 5.24505821,9.58025629 C4.49180119,10.4291662 1.93072733,9.70509599 1.12725318,10.5040701 C0.323779026,11.3030441 3.58789277,12.4266014 3.58789277,12.4266014 L3.58789277,12.4266014 C3.58789277,12.4266014 4.71777829,15.6724336 5.52125245,14.8734595 Z M11.6800643,3.0786802 C11.6543408,3.05329949 11.6286174,3.02791878 11.6028939,2.97715736 C11.6028939,2.97715736 11.6028939,2.95177665 11.5771704,2.95177665 C11.5514469,2.92639594 11.5514469,2.90101523 11.5514469,2.87563452 C11.5514469,2.87563452 11.5514469,2.85025381 11.5257235,2.85025381 C11.5257235,2.8248731 11.5,2.79949239 11.5,2.74873096 C11.5,2.74873096 11.5,2.74873096 11.5,2.74873096 C11.5,2.72335025 11.5,2.67258883 11.5,2.64720812 C11.5,2.64720812 11.5,2.62182741 11.5,2.62182741 C11.5,2.5964467 11.5,2.57106599 11.5,2.54568528 C11.5,2.52030457 11.5,2.52030457 11.5,2.49492386 C11.5,2.46954315 11.5257235,2.44416244 11.5257235,2.41878173 C11.5257235,2.41878173 11.5257235,2.39340102 11.5257235,2.39340102 C11.5514469,2.31725888 11.6028939,2.24111675 11.6543408,2.19035533 C11.9115756,1.93654822 12.3231511,1.93654822 12.5546624,2.19035533 C12.7861736,2.41878173 12.8118971,2.74873096 12.6318328,3.00253807 C12.6061093,3.02791878 12.5803859,3.05329949 12.5546624,3.0786802 C12.3488746,3.3071066 11.937299,3.3071066 11.6800643,3.0786802 Z" id="path-1"></path>
    </defs>
    <g id="ic_master_ai_fish" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <mask id="mask-2" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <use id="fish" fill="#000000" fill-rule="nonzero" xlink:href="#path-1"></use>
        <g id="编组" mask="url(#mask-2)" fill="#000000" fill-rule="nonzero">
            <g id="ic/bg/white">
                <rect id="Rectangle-13-Copy" x="0" y="0" width="16" height="16"></rect>
            </g>
        </g>
    </g>
</svg>