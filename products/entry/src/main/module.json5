{
  module: {
    name: "entry",
    type: "entry",
    description: "$string:module_desc",
    mainElement: "EntryAbility",
    deviceTypes: ["phone"],
    deliveryWithInstall: true,
    pages: "$profile:main_pages",
    routerMap: "$profile:router_map",
    requestPermissions: [
      {
        name: "ohos.permission.INTERNET",
      },
      {
        name: "ohos.permission.APP_TRACKING_CONSENT",
        reason: "$string:app_name",
        usedScene: {
          abilities: ["EntryAbility"],
          when: "inuse",
        },
      },
    ],
    metadata: [
      {
        name: "client_id",
        // todo 替换client_id值,
        value: "xxxx",
      },
    ],
    abilities: [
      {
        name: "EntryAbility",
        srcEntry: "./ets/entryability/EntryAbility.ets",
        description: "$string:EntryAbility_desc",
        icon: "$media:icon",
        label: "$string:EntryAbility_label",
        startWindowIcon: "$media:startIcon",
        startWindowBackground: "$color:start_window_background",
        exported: true,
        skills: [
          {
            entities: ["entity.system.home"],
            actions: ["action.system.home"],
          },
        ],
      },
    ],
    extensionAbilities: [
      {
        name: "EntryFormAbility",
        srcEntry: "./ets/entryformability/EntryFormAbility.ets",
        label: "$string:EntryFormAbility_label",
        description: "$string:EntryFormAbility_desc",
        type: "form",
        metadata: [
          {
            name: "ohos.extension.form",
            resource: "$profile:form_config",
          },
        ],
      },
    ],
  },
}
