import { Channel, LoginService, LoginType } from 'aggregated_login';
import { AccountUtil, buildTitleBar, RouterMap, RouterModule, UserInfo } from 'commonlib';

@Builder
export function QuickLoginPageBuilder() {
  QuickLoginPage();
}

@ComponentV2
export struct QuickLoginPage {
  @Local nextRouter: string = '';

  aboutToAppear(): void {
    this.getRouterParams()
  }

  getRouterParams() {
    const params: Record<string, string> | undefined = RouterModule.getNavParam(RouterMap.QUICK_LOGIN_PAGE)
    if (params?.url) {
      this.nextRouter = params.url
    }
  }

  build() {
    NavDestination() {
      Scroll() {
        LoginService({
          icon: $r('app.media.startIcon'),
          privacyPolicyEvent: () => {
            RouterModule.push(RouterMap.PRIVACY_POLICY_DETAIL_PAGE);
          },
          loginBtnBgColor: '#FF0000',
          termOfServiceEvent: () => {
            RouterModule.push(RouterMap.TERMS_OF_SERVICE_PAGE);
          },
          loginTypes: [new Channel(LoginType.WECHAT, '微信登录', {
            appId: 'wxd5a474c635b8fd17',
            scope: 'snsapi_userinfo,snsapi_friend,snsapi_message,snsapi_contact',
            transaction: 'test123',
            state: 'none',
          }, $r('app.media.wechat'))],
          pathInfos: RouterModule.getStack(),
          loginFinishedCb: (flag: boolean, unionID?: string) => {
            // 模板忽略登录失败场景
            this.loginSuccess(unionID || '0')
          },
        });
      }.backgroundColor('#F1F3F5')
      .edgeEffect(EdgeEffect.Spring);
    }
    .title(buildTitleBar(''))
    .width('100%')
    .height('100%')
    .backgroundColor('#F1F3F5');
  }

  loginSuccess(unionID: string) {
    // 模拟登录
    let data: UserInfo = {
      id: 0,
      avatar: 'default_avatar',
      name: '',
      nickname: '华为用户',
      sex: '',
      cellphone: '',
      birthday: '',
      code: unionID,
      isLogin: true,
      isMembership: false,
    }
    AccountUtil.updateUserInfo(data);
    if (this.nextRouter) {
      RouterModule.replace(this.nextRouter)
    } else {
      RouterModule.pop();
    }
  }
}