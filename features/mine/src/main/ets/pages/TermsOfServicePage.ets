import { buildTitleBar } from 'commonlib';
import { LengthMetrics } from '@kit.ArkUI';

@Builder
export function TermsOfServicePageBuilder() {
  TermsOfServicePage();
}

@ComponentV2
export struct TermsOfServicePage {
  build() {
    NavDestination() {
      Scroll() {
        Text('菜谱 App 隐私协议\n' +
          '本隐私协议适用于 [App 开发者 / 公司名称] 开发和运营的菜谱 App（以下简称 “本 App”）。我们深知个人信息对您的重要性，始终致力于保护您的隐私。本隐私协议将向您详细说明我们如何收集、使用、存储和共享您的个人信息，以及您所拥有的权利。在使用本 App 前，请仔细阅读并理解本隐私协议的内容。\n' +
          '一、我们如何收集您的个人信息\n' +
          '（一）注册与登录信息\n' +
          '当您注册或登录本 App 时，我们可能会收集您的手机号码、电子邮箱地址、用户名和密码。手机号码和电子邮箱用于验证您的身份及后续的账号安全操作，如找回密码等。用户名用于展示您在 App 内的身份标识。若您选择使用第三方账号（如微信、QQ 等）登录，我们会在您授权同意的情况下，获取您在第三方平台的昵称、头像等公开信息，以便为您提供便捷的登录服务。\n' +
          '（二）使用过程中收集的信息\n' +
          '搜索记录：当您在 App 内进行菜谱搜索时，我们会收集您输入的关键词。这些信息有助于为您提供更精准的搜索结果，且通常情况下无法单独识别您的个人身份。但当搜索关键词与其他可识别信息结合时，将按照个人信息进行处理与保护。\n' +
          '浏览与收藏记录：我们会记录您浏览过的菜谱页面、收藏的菜谱内容等信息。通过这些信息，我们可以为您提供个性化的推荐服务，例如向您推荐您可能感兴趣的相似菜谱。\n' +
          '发布与互动信息：若您在 App 内发布菜谱、评论、点赞等，我们会收集您发布的文字、图片、视频等内容。同时，我们会记录您与其他用户的互动情况，如您回复他人评论等信息，以维护 App 内的社交互动环境。\n' +
          '（三）设备信息\n' +
          '为保障 App 的稳定运行及安全，我们会收集您的设备相关信息，包括设备型号、操作系统版本、唯一设备标识符（如 IMEI/IDFA 等）、IP 地址、浏览器类型等。这些信息有助于我们分析设备兼容性问题、排查故障以及防范安全风险。\n' +
          '（四）位置信息\n' +
          '当您使用与地理位置相关的功能（如根据所在地区推荐特色菜谱）时，我们会在获得您明确授权后，收集您的地理位置信息。您可以随时在设备设置中关闭位置权限，停止向我们提供位置信息，这不会影响您使用 App 的其他基本功能。\n' +
          '二、我们如何使用您的个人信息\n' +
          '提供服务：使用您的注册信息为您提供登录、账号管理等基础服务；依据您的搜索、浏览、收藏等记录为您提供个性化的菜谱推荐、内容展示等服务；利用您发布的信息展示您的作品，促进用户间的交流互动。\n' +
          '改进服务：分析您的使用行为数据，了解用户需求和偏好，优化 App 的功能和界面设计，提升用户体验。例如，根据多数用户的搜索习惯，优化搜索算法，提高搜索效率。\n' +
          '安全保障：利用设备信息和位置信息等进行账号安全监测，防范账号被盗用、恶意攻击等安全风险，保障您在 App 内的信息安全。\n' +
          '通知与沟通：使用您提供的手机号码、电子邮箱等信息，向您发送重要通知，如系统更新、服务变更等信息。同时，在您主动联系我们时，用于与您沟通解决问题。\n' +
          '三、我们如何存储您的个人信息\n' +
          '存储地点：我们会将收集到的您的个人信息存储在中国境内。若因业务需要将数据传输至境外，我们会按照相关法律法规的要求，采取必要的安全措施保障您的个人信息安全。\n' +
          '存储期限：我们仅在为实现本隐私协议所述目的所必需的最短时间内保留您的个人信息。超出存储期限后，我们会对您的个人信息进行删除或匿名化处理。例如，对于已注销账号用户的个人信息，在符合法律规定的前提下，我们将尽快进行删除处理。\n' +
          '四、我们如何共享、转让和公开披露您的个人信息\n' +
          '（一）共享\n' +
          '第三方服务提供商：为了向您提供更全面、优质的服务，我们可能会将您的部分个人信息共享给第三方服务提供商，如用于数据分析以改进 App 性能的合作伙伴、推送通知服务提供商等。我们会与这些第三方签订严格的保密协议，要求他们按照我们的规定和相关法律法规处理您的个人信息，不得将其用于其他任何目的。\n' +
          '广告合作伙伴：在您同意的情况下，我们可能会与广告合作伙伴共享您的部分非敏感个人信息（如设备信息、浏览行为信息等），以便为您提供更符合您兴趣的广告。但我们不会共享您的敏感个人信息（如身份证号、银行卡号等）。\n' +
          '（二）转让\n' +
          '我们不会将您的个人信息转让给其他公司、组织或个人，除非事先获得您的明确同意，或根据法律法规的要求、政府部门的指令等必须进行转让的情形。在进行转让前，我们会确保新的接收方具备合法处理您个人信息的能力，并要求其遵守本隐私协议的规定。\n' +
          '（三）公开披露\n' +
          '我们不会公开披露您的个人信息，除非获得您的明确同意，或根据法律法规的要求必须进行公开披露，如司法机关依法要求我们提供相关信息等。在公开披露前，我们会对公开披露的信息进行必要的脱敏处理，以最大程度保护您的隐私。\n' +
          '五、您的权利\n' +
          '访问权：您有权随时访问您在本 App 内的个人信息，包括注册信息、收藏的菜谱、发布的内容等。您可以通过 App 内的个人信息管理界面进行查看和管理。\n' +
          '更正权：如果您发现我们收集、存储的您的个人信息存在错误或不完整，您有权要求我们进行更正。您可以通过 App 内的反馈渠道或联系我们的客服人员提出更正申请。\n' +
          '删除权：在符合法律法规规定的情形下，您有权要求我们删除您的个人信息。例如，当您注销账号时，我们将删除与您账号相关的所有个人信息。但在某些情况下，根据法律法规的要求，我们可能无法立即删除您的所有信息，我们会在合理的期限内完成删除操作。\n' +
          '撤回同意权：对于需要您授权同意的个人信息收集和使用行为，您有权随时撤回您的同意。您可以通过设备设置或 App 内的相关功能设置，撤回对特定权限的授权，如位置权限、相机权限等。撤回同意后，我们将不再基于该授权收集和使用您的个人信息，但不影响我们基于您之前已同意的授权所进行的合法处理行为。\n' +
          '注销账号权：您有权随时注销您在本 App 内的账号。您可以在 App 内的账号设置中找到注销账号的入口，按照提示操作进行注销。注销账号后，我们将删除与您账号相关的所有个人信息，但法律法规另有规定的除外。\n' +
          '六、我们如何保护您的个人信息安全\n' +
          '技术措施：我们采用多种安全技术手段保护您的个人信息安全，如加密存储、访问控制、数据脱敏等。对您的个人信息进行加密传输和存储，防止信息在传输和存储过程中被窃取或篡改。\n' +
          '组织措施：我们建立了完善的信息安全管理体系，明确各部门和人员在个人信息保护方面的职责，定期对员工进行信息安全培训，提高员工的信息安全意识和专业技能。\n' +
          '应急响应：我们制定了信息安全事件应急预案，当发生个人信息安全事件时，我们会立即启动应急预案，采取措施降低事件对您的影响，并按照法律法规的要求及时向您和相关监管部门报告。\n' +
          '七、未成年人保护\n' +
          '我们非常重视对未成年人个人信息的保护。本 App 不面向 14 周岁以下的未成年人提供服务。若我们发现收集了 14 周岁以下未成年人的个人信息，我们将尽快删除相关信息。如果您是未成年人的监护人，发现您监护的未成年人在未经您同意的情况下使用本 App 并提供了个人信息，请您及时与我们联系，我们将协助您进行处理。\n' +
          '八、隐私协议的更新\n' +
          '我们可能会根据法律法规的变化、业务发展需要或用户反馈等原因，对本隐私协议进行更新。更新后的隐私协议将在 App 内显著位置公布，并以弹窗、站内信等方式通知您。请您定期关注本隐私协议的更新内容。若您在隐私协议更新后继续使用本 App，即视为您同意接受更新后的隐私协议的约束。\n' +
          '九、联系我们\n' +
          '如果您对本隐私协议有任何疑问、意见或建议，或者在使用本 App 过程中发现个人信息安全问题，请通过以下方式联系我们：\n' +
          '联系邮箱：[邮箱地址]\n' +
          '联系电话：[电话号码]\n' +
          '我们将在收到您的反馈后，尽快与您取得联系并处理您的问题。\n' +
          '[App 开发者 / 公司名称]\n' +
          '[具体日期]\n');
      }
      .edgeEffect(EdgeEffect.Spring)
      .padding(16)
      .height('100%')
      .align(Alignment.Top);
    }
    .title(buildTitleBar('隐私政策'), { paddingStart: LengthMetrics.vp(16) });
  }
}