export enum LoginErrorCode {
  ERROR_CODE_NETWORK_ERROR = 1001502005,
  ERROR_CODE_AGREEMENT_STATUS_NOT_ACCEPTED = 1005300001,
  ERROR_CODE_LOGIN_OUT = 1001502001,
  ERROR_CODE_NOT_SUPPORTED = 1001500003,
  ERROR_CODE_NOT_REQUIRED_SCOPE_OR_PERMISSION = 1001502014
}

export enum NoticeTypeEnum {
  NORMAL = 1,
  RECIPE
}

export interface PersonalPopInfo {
  type: number,
  avatar: ResourceStr,
  nickname: string,
}