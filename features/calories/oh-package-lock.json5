{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/axios@^2.2.4": "@ohos/axios@2.2.6", "@ohos/mpchart@3.0.21": "@ohos/mpchart@3.0.21", "base_ui@../../components/base_ui": "base_ui@../../components/base_ui", "calorie_calculation@../../components/calorie_calculation": "calorie_calculation@../../components/calorie_calculation", "commonlib@../../commons/commonlib": "commonlib@../../commons/commonlib", "network@../../commons/network": "network@../../commons/network"}, "packages": {"@ohos/axios@2.2.6": {"name": "@ohos/axios", "version": "2.2.6", "integrity": "sha512-A1JqGe6XaeqWyjQETitFW4EkubQS7Fv7h0YG5a/ry3/a/vOgVGzwC4y5KAhvMzVv1tYjfY0ntMtV2kJGlmOHcQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/axios/-/axios-2.2.6.har", "registryType": "ohpm"}, "@ohos/mpchart@3.0.21": {"name": "@ohos/mpchart", "version": "3.0.21", "integrity": "sha512-3Ozqe/hIx4kwjxTsgeb07elKwa4K43mO//XRknsLdLIbz/oQsRNGKvNQI0Ooy3tfrkTofQOI5kW1rA6gUJQDyg==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/mpchart/-/mpchart-3.0.21.har", "registryType": "ohpm"}, "base_ui@../../components/base_ui": {"name": "base_ui", "version": "1.0.0", "resolved": "../../components/base_ui", "registryType": "local"}, "calorie_calculation@../../components/calorie_calculation": {"name": "calorie_calculation", "version": "1.0.0", "resolved": "../../components/calorie_calculation", "registryType": "local", "dependencies": {"@ohos/mpchart": "3.0.21", "base_ui": "file:../../components/base_ui"}}, "commonlib@../../commons/commonlib": {"name": "commonlib", "version": "1.0.0", "resolved": "../../commons/commonlib", "registryType": "local"}, "network@../../commons/network": {"name": "network", "version": "1.0.0", "resolved": "../../commons/network", "registryType": "local", "dependencies": {"@ohos/axios": "^2.2.4"}}}}