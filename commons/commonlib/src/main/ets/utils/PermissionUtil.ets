import { abilityAccessCtrl, bundleManager, common, Permissions } from '@kit.AbilityKit';
import { BusinessError, Callback } from '@kit.BasicServicesKit';
import { Logger } from './Logger';
import { authentication } from '@kit.AccountKit';
import { util } from '@kit.ArkTS';

const TAG: string = '[PermissionUtil]';

enum PermissionStatus {
  GRANTED = 0,
  DENIED = -1,
  UNKNOWN = 2,
}

/**
 * 取消授权，每次申请关联手机号都要拉起面板
 */
export function cancelAuthorization() {
  const cancelRequest: authentication.CancelAuthorizationRequest =
    new authentication.HuaweiIDProvider().createCancelAuthorizationRequest();
  cancelRequest.state = util.generateRandomUUID();
  try {
    const controller: authentication.AuthenticationController = new authentication.AuthenticationController();
    controller.executeRequest(cancelRequest).then((data: authentication.AuthenticationResponse) => {
      const cancelAuthorizationResponse = data as authentication.CancelAuthorizationResponse;
      const state: string = cancelAuthorizationResponse.state!;
      if (cancelRequest.state !== state) {
        Logger.error('Failed to cancel. The state is different');
        return;
      }
    }).catch((err: BusinessError) => {
      Logger.error(`Failed to cancel. errCode is ${err.code}, errMessage is ${err.message}`);
    })
  } catch (err) {
    Logger.error(`Failed to cancel. errCode is ${err.code}, errMessage is ${err.message}`);
  }
}

export class PermissionUtil {
  private static _atManager: abilityAccessCtrl.AtManager = abilityAccessCtrl.createAtManager();
  private static _context: Context;

  public static async requestPermissions(permissions: Permissions[], callback?: Callback<void>) {
    const needAuthorized: Permissions[] = [];
    for (let permission of permissions) {
      const isGranted: boolean = await PermissionUtil._checkPermissions(permission);
      if (!isGranted) {
        needAuthorized.push(permission);
      }
    }
    if (!needAuthorized.length) {
      return;
    }
    if (!PermissionUtil._context) {
      PermissionUtil._context = getContext() as common.UIAbilityContext;
    }
    // 向用户申请授权
    PermissionUtil._atManager.requestPermissionsFromUser(PermissionUtil._context, needAuthorized).then((data) => {
      let grantStatus: number[] = data.authResults;
      let length: number = grantStatus.length;
      let permissive = true;
      for (let i = 0; i < length; i++) {
        if (grantStatus[i] === PermissionStatus.GRANTED) {
          Logger.info(TAG, 'request permission success::' + needAuthorized[i]);
          return;
        } else if (grantStatus[i] === PermissionStatus.DENIED) {
          PermissionUtil._requestPermissionsOnSetting(needAuthorized[i]);
          return;
        } else {
          permissive = false;
          Logger.info(TAG, 'request permission failed:: user denied');
          return;
        }
      }
      if (permissive) {
        callback?.();
      }
    }).catch((err: BusinessError) => {
      Logger.error(TAG, `Failed to request permissions from user. Code is ${err.code}, message is ${err.message}`);
    });
  }

  // 用户首次拒绝后二次申请授权
  private static async _requestPermissionsOnSetting(permissions: Permissions) {
    PermissionUtil._atManager.requestPermissionOnSetting(PermissionUtil._context, [permissions])
      .then((data: abilityAccessCtrl.GrantStatus[]) => {
        Logger.info(TAG, 'request permission on setting success::' + JSON.stringify(data));
      })
      .catch((err: BusinessError) => {
        Logger.error(TAG,
          `Failed to request permissions on setting from user. Code is ${err.code}, message is ${err.message}`);
      });
  }

  private static async _checkPermissions(permission: Permissions): Promise<boolean> {
    let grantStatus: abilityAccessCtrl.GrantStatus = await PermissionUtil._checkAccessToken(permission);
    if (grantStatus === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
      return true;
    }
    return false;
  }

  // 校验应用是否被授予权限
  private static async _checkAccessToken(permission: Permissions): Promise<abilityAccessCtrl.GrantStatus> {
    let grantStatus: abilityAccessCtrl.GrantStatus = abilityAccessCtrl.GrantStatus.PERMISSION_DENIED;
    let tokenId: number = 0;
    try {
      let bundleInfo: bundleManager.BundleInfo =
        bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      let appInfo: bundleManager.ApplicationInfo = bundleInfo.appInfo;
      tokenId = appInfo.accessTokenId;
    } catch (error) {
      let err: BusinessError = error as BusinessError;
      Logger.error(TAG, `Failed to get bundle info for self. Code is ${err.code}, message is ${err.message}`);
    }
    try {
      grantStatus = await PermissionUtil._atManager.checkAccessToken(tokenId, permission);
    } catch (error) {
      let err: BusinessError = error as BusinessError;
      Logger.error(TAG, `Failed to check access token. Code is ${err.code}, message is ${err.message}`);
    }
    return grantStatus;
  }
}