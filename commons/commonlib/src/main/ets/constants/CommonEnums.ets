export enum AppStorageMap {
  HUAWEI_ACCOUNT = 'huaweiAccount',
  USER_INFO = 'userInfo',
  SHOPPING_BASKET = 'shoppingBasket',
}

export enum RouterMap {
  // 启动页
  LAUNCH_PAGE = 'LaunchPage',
  // 广告页
  LAUNCH_AD_PAGE = 'LaunchAdPage',
  //  主页
  MAIN_ENTRY = 'MainEntry',
  //  隐私协议
  PRIVACY_POLICY_PAGE = 'PrivacyPolicyPage',
  //  首页
  HOME = 'HomePage',
  //  菜品详情
  DISHES = 'DishesPage',
  //  搜索页面
  SEARCH = 'SearchPage',
  //  浏览记录
  BROWSING_HISTORY = 'BrowsingHistory',
  //  我的收藏
  MY_COLLECTION = 'MyCollection',
  //  设置
  SETTINGS_PAGE = 'SettingsPage',
  //  个人信息
  PERSON_INFO_PAGE = 'PersonalInfo',
  //  隐私协议
  PRIVACY_PAGE = 'PrivacyPage',
  //  清除缓存
  CACHE_PAGE = 'CachePage',
  //  隐私政策
  TERMS_OF_SERVICE_PAGE = 'TermsOfServicePage',
  //  用户协议
  PRIVACY_POLICY_DETAIL_PAGE = 'PrivacyPolicyDetailPage',
  //  快速登录
  QUICK_LOGIN_PAGE = 'QuickLoginPage',
  // 菜篮子
  SHOPPING_BASKET_PAGE = 'ShoppingBasketPage',
  // 上传菜谱
  UPLOAD_RECIPE_PAGE = 'UploadRecipePage',
  // 饮食计划
  DIET_PLAN_PAGE = 'DietPlanPage',
  // 博主个人主页
  BLOGGER_PROFILE_PAGE = 'BloggerProfilePage',
  // 博主关注的人
  FOLLOWERS_PAGE = 'FollowersPage',
  // 侧边栏菜单
  SIDE_BAR_PAGE = 'SideBarPage',
  // 消息中心
  NOTICE_CENTER_PAGE = 'NoticeCenterPage',
  // 消息详情
  NOTICE_DETAIL_PAGE = 'NoticeDetailPage',
  // 食物搜索
  SEARCH_FOOD_PAGE = 'SearchFoodPage',
  // 会员中心
  MEMBER_CENTER_PAGE = 'MemberCenterPage',
  // 会员协议
  MEMBERSHIP_PRIVACY = 'MembershipPrivacy',
}

